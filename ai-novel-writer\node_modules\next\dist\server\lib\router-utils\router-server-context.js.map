{"version": 3, "sources": ["../../../../src/server/lib/router-utils/router-server-context.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'node:http'\nimport type { NextConfigComplete } from '../../config-shared'\nimport type { UrlWithParsedQuery } from 'node:url'\n\nexport type RevalidateFn = (config: {\n  urlPath: string\n  revalidateHeaders: { [key: string]: string | string[] }\n  opts: { unstable_onlyGenerated?: boolean }\n}) => Promise<void>\n\n// The RouterServerContext contains instance specific\n// information that isn't available/relevant when\n// deployed in serverless environments, the key is\n// the relative project dir this allows separate contexts\n// when running multiple next instances in same process\nexport type RouterServerContext = Record<\n  string,\n  {\n    // hostname the server is started with\n    hostname?: string\n    // revalidate function to bypass going through network\n    // to invoke revalidate request (uses mocked req/res)\n    revalidate?: RevalidateFn\n    // function to render the 404 page\n    render404?: (\n      req: IncomingMessage,\n      res: ServerResponse,\n      parsedUrl?: UrlWithParsedQuery,\n      setHeaders?: boolean\n    ) => Promise<void>\n    // current loaded public runtime config\n    publicRuntimeConfig?: NextConfigComplete['publicRuntimeConfig']\n    // exposing nextConfig for dev mode specifically\n    nextConfig?: NextConfigComplete\n    // whether running in custom server mode\n    isCustomServer?: boolean\n    // whether test proxy is enabled\n    experimentalTestProxy?: boolean\n    // allow dev server to log with original stack\n    logErrorWithOriginalStack?: (err: unknown, type: string) => void\n    // allow setting ISR status in dev\n    setIsrStatus?: (key: string, value: boolean | null) => void\n  }\n>\n\nexport const RouterServerContextSymbol = Symbol.for(\n  '@next/router-server-methods'\n)\n\nexport const routerServerGlobal = globalThis as typeof globalThis & {\n  [RouterServerContextSymbol]?: RouterServerContext\n}\n"], "names": ["RouterServerContextSymbol", "routerServerGlobal", "Symbol", "for", "globalThis"], "mappings": ";;;;;;;;;;;;;;;IA6CaA,yBAAyB;eAAzBA;;IAIAC,kBAAkB;eAAlBA;;;AAJN,MAAMD,4BAA4BE,OAAOC,GAAG,CACjD;AAGK,MAAMF,qBAAqBG", "ignoreList": [0]}