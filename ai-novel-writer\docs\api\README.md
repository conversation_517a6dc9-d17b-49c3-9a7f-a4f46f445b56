# API 文档

AI小说写作应用的 REST API 文档。

## 基础信息

- **基础URL**: `https://your-domain.com/api`
- **认证方式**: Bearer <PERSON>ken (Supabase JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

所有API请求都需要在请求头中包含有效的认证令牌：

```
Authorization: Bearer <your-jwt-token>
```

## 响应格式

### 成功响应

```json
{
  "data": {
    // 响应数据
  }
}
```

### 错误响应

```json
{
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

## 状态码

- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求过于频繁
- `500` - 服务器内部错误

## API 端点

### 用户管理

#### 获取用户信息
```
GET /api/user
```

**响应示例**:
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "username": "username",
    "avatar_url": "https://...",
    "subscription_type": "free",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 更新用户信息
```
PUT /api/user
```

**请求体**:
```json
{
  "username": "new_username",
  "avatar_url": "https://..."
}
```

### 小说管理

#### 获取小说列表
```
GET /api/novels
```

**查询参数**:
- `status` (可选): 小说状态 (`draft`, `writing`, `completed`, `published`, `archived`)
- `genre` (可选): 小说类型
- `sortBy` (可选): 排序字段 (`created_at`, `updated_at`, `title`, `word_count`)
- `sortOrder` (可选): 排序方向 (`asc`, `desc`)
- `limit` (可选): 返回数量限制 (默认: 20, 最大: 100)
- `offset` (可选): 偏移量 (默认: 0)

**响应示例**:
```json
{
  "novels": [
    {
      "id": "uuid",
      "title": "小说标题",
      "description": "小说描述",
      "genre": "fantasy",
      "status": "writing",
      "is_public": false,
      "word_count": 50000,
      "chapter_count": 10,
      "tags": ["标签1", "标签2"],
      "cover_url": "https://...",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "hasMore": false
}
```

#### 创建新小说
```
POST /api/novels
```

**请求体**:
```json
{
  "title": "小说标题",
  "description": "小说描述",
  "genre": "fantasy",
  "tags": ["标签1", "标签2"],
  "is_public": false
}
```

#### 获取小说详情
```
GET /api/novels/[id]
```

#### 更新小说
```
PUT /api/novels/[id]
```

#### 删除小说
```
DELETE /api/novels/[id]
```

### 章节管理

#### 获取章节列表
```
GET /api/chapters?novel_id=uuid
```

**查询参数**:
- `novel_id` (必需): 小说ID
- `status` (可选): 章节状态
- `limit` (可选): 返回数量限制
- `offset` (可选): 偏移量

#### 创建新章节
```
POST /api/chapters
```

**请求体**:
```json
{
  "novel_id": "uuid",
  "title": "章节标题",
  "content": "章节内容",
  "notes": "章节备注",
  "tags": ["标签"],
  "target_word_count": 2000
}
```

#### 获取章节详情
```
GET /api/chapters/[id]
```

#### 更新章节
```
PUT /api/chapters/[id]
```

#### 删除章节
```
DELETE /api/chapters/[id]
```

### AI 服务

#### AI 文本生成
```
POST /api/ai/generate
```

**请求体**:
```json
{
  "prompt": "写作提示",
  "context": "上下文内容",
  "type": "continuation",
  "model": "gpt-3.5-turbo",
  "max_tokens": 500,
  "temperature": 0.7
}
```

**响应示例**:
```json
{
  "generated_text": "AI生成的文本内容",
  "usage": {
    "prompt_tokens": 100,
    "completion_tokens": 200,
    "total_tokens": 300
  },
  "model": "gpt-3.5-turbo"
}
```

#### 测试AI连接
```
GET /api/ai/test
```

#### 获取提示词列表
```
GET /api/ai/prompts
```

#### 创建提示词
```
POST /api/ai/prompts
```

### 人物管理

#### 获取人物列表
```
GET /api/characters?novel_id=uuid
```

#### 创建新人物
```
POST /api/characters
```

#### 更新人物
```
PUT /api/characters/[id]
```

#### 删除人物
```
DELETE /api/characters/[id]
```

### 大纲管理

#### 获取大纲列表
```
GET /api/outlines?novel_id=uuid
```

#### 创建大纲项目
```
POST /api/outlines
```

#### 更新大纲项目
```
PUT /api/outlines/[id]
```

#### 删除大纲项目
```
DELETE /api/outlines/[id]
```

### 写作笔记

#### 获取笔记列表
```
GET /api/notes
```

#### 创建新笔记
```
POST /api/notes
```

#### 更新笔记
```
PUT /api/notes/[id]
```

#### 删除笔记
```
DELETE /api/notes/[id]
```

### 统计数据

#### 获取写作统计
```
GET /api/stats/writing
```

#### 获取目标列表
```
GET /api/goals
```

#### 创建新目标
```
POST /api/goals
```

### 系统管理 (管理员)

#### 获取系统统计
```
GET /api/admin/stats
```

#### 获取用户列表
```
GET /api/admin/users
```

#### 获取内容举报
```
GET /api/admin/reports
```

## 错误代码

| 代码 | 说明 |
|------|------|
| `INVALID_REQUEST` | 请求参数无效 |
| `UNAUTHORIZED` | 未授权访问 |
| `FORBIDDEN` | 权限不足 |
| `NOT_FOUND` | 资源不存在 |
| `RATE_LIMITED` | 请求频率超限 |
| `AI_SERVICE_ERROR` | AI服务错误 |
| `DATABASE_ERROR` | 数据库错误 |
| `INTERNAL_ERROR` | 服务器内部错误 |

## 限制说明

### 请求频率限制

- 普通用户: 100 请求/分钟
- VIP用户: 500 请求/分钟
- 管理员: 1000 请求/分钟

### 内容限制

- 小说标题: 最大 200 字符
- 小说描述: 最大 2000 字符
- 章节标题: 最大 200 字符
- 章节内容: 最大 100,000 字符
- 标签数量: 最大 10 个
- 标签长度: 最大 50 字符

### AI 服务限制

- 免费用户: 10 次/天
- VIP用户: 1000 次/天
- 单次请求最大 token 数: 4000

## SDK 和示例

### JavaScript/TypeScript

```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// 获取小说列表
async function getNovels() {
  const { data, error } = await supabase
    .from('novels')
    .select('*')
    .order('updated_at', { ascending: false })
  
  return { data, error }
}

// 创建新小说
async function createNovel(novel: {
  title: string
  description?: string
  genre?: string
}) {
  const { data, error } = await supabase
    .from('novels')
    .insert(novel)
    .select()
    .single()
  
  return { data, error }
}
```

### Python

```python
import requests

class NovelWriterAPI:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def get_novels(self):
        response = requests.get(
            f'{self.base_url}/api/novels',
            headers=self.headers
        )
        return response.json()
    
    def create_novel(self, novel_data: dict):
        response = requests.post(
            f'{self.base_url}/api/novels',
            json=novel_data,
            headers=self.headers
        )
        return response.json()
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础的小说和章节管理API
- AI文本生成功能
- 用户认证和权限管理
