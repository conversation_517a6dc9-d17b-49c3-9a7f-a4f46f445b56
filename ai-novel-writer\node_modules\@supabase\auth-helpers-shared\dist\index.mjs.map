{"version": 3, "sources": ["../../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js", "../src/browserCookieStorage.ts", "../src/utils/cookies.ts", "../src/utils/helpers.ts", "../src/utils/constants.ts", "../src/chunker.ts", "../src/cookieAuthStorageAdapter.ts", "../src/createClient.ts"], "sourcesContent": ["/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nexports.parse = parse;\nexports.serialize = serialize;\n\n/**\n * Module variables.\n * @private\n */\n\nvar __toString = Object.prototype.toString\n\n/**\n * RegExp to match field-content in RFC 7230 sec 3.2\n *\n * field-content = field-vchar [ 1*( SP / HTAB ) field-vchar ]\n * field-vchar   = VCHAR / obs-text\n * obs-text      = %x80-FF\n */\n\nvar fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [options]\n * @return {object}\n * @public\n */\n\nfunction parse(str, options) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string');\n  }\n\n  var obj = {}\n  var opt = options || {};\n  var dec = opt.decode || decode;\n\n  var index = 0\n  while (index < str.length) {\n    var eqIdx = str.indexOf('=', index)\n\n    // no more cookie pairs\n    if (eqIdx === -1) {\n      break\n    }\n\n    var endIdx = str.indexOf(';', index)\n\n    if (endIdx === -1) {\n      endIdx = str.length\n    } else if (endIdx < eqIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(';', eqIdx - 1) + 1\n      continue\n    }\n\n    var key = str.slice(index, eqIdx).trim()\n\n    // only assign once\n    if (undefined === obj[key]) {\n      var val = str.slice(eqIdx + 1, endIdx).trim()\n\n      // quoted values\n      if (val.charCodeAt(0) === 0x22) {\n        val = val.slice(1, -1)\n      }\n\n      obj[key] = tryDecode(val, dec);\n    }\n\n    index = endIdx + 1\n  }\n\n  return obj;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize the a name value pair into a cookie string suitable for\n * http headers. An optional options object specified cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [options]\n * @return {string}\n * @public\n */\n\nfunction serialize(name, val, options) {\n  var opt = options || {};\n  var enc = opt.encode || encode;\n\n  if (typeof enc !== 'function') {\n    throw new TypeError('option encode is invalid');\n  }\n\n  if (!fieldContentRegExp.test(name)) {\n    throw new TypeError('argument name is invalid');\n  }\n\n  var value = enc(val);\n\n  if (value && !fieldContentRegExp.test(value)) {\n    throw new TypeError('argument val is invalid');\n  }\n\n  var str = name + '=' + value;\n\n  if (null != opt.maxAge) {\n    var maxAge = opt.maxAge - 0;\n\n    if (isNaN(maxAge) || !isFinite(maxAge)) {\n      throw new TypeError('option maxAge is invalid')\n    }\n\n    str += '; Max-Age=' + Math.floor(maxAge);\n  }\n\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n\n    str += '; Domain=' + opt.domain;\n  }\n\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n\n    str += '; Path=' + opt.path;\n  }\n\n  if (opt.expires) {\n    var expires = opt.expires\n\n    if (!isDate(expires) || isNaN(expires.valueOf())) {\n      throw new TypeError('option expires is invalid');\n    }\n\n    str += '; Expires=' + expires.toUTCString()\n  }\n\n  if (opt.httpOnly) {\n    str += '; HttpOnly';\n  }\n\n  if (opt.secure) {\n    str += '; Secure';\n  }\n\n  if (opt.priority) {\n    var priority = typeof opt.priority === 'string'\n      ? opt.priority.toLowerCase()\n      : opt.priority\n\n    switch (priority) {\n      case 'low':\n        str += '; Priority=Low'\n        break\n      case 'medium':\n        str += '; Priority=Medium'\n        break\n      case 'high':\n        str += '; Priority=High'\n        break\n      default:\n        throw new TypeError('option priority is invalid')\n    }\n  }\n\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string'\n      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n *\n * @param {string} str\n * @returns {string}\n */\n\nfunction decode (str) {\n  return str.indexOf('%') !== -1\n    ? decodeURIComponent(str)\n    : str\n}\n\n/**\n * URL-encode value.\n *\n * @param {string} str\n * @returns {string}\n */\n\nfunction encode (val) {\n  return encodeURIComponent(val)\n}\n\n/**\n * Determine if value is a Date.\n *\n * @param {*} val\n * @private\n */\n\nfunction isDate (val) {\n  return __toString.call(val) === '[object Date]' ||\n    val instanceof Date\n}\n\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */\n\nfunction tryDecode(str, decode) {\n  try {\n    return decode(str);\n  } catch (e) {\n    return str;\n  }\n}\n", "import { parse, serialize } from 'cookie';\nimport { CookieAuthStorageAdapter } from './cookieAuthStorageAdapter';\nimport { CookieOptions } from './types';\nimport { isBrowser } from './utils';\n\nexport class BrowserCookieAuthStorageAdapter extends CookieAuthStorageAdapter {\n\tconstructor(cookieOptions?: CookieOptions) {\n\t\tsuper(cookieOptions);\n\t}\n\n\tprotected getCookie(name: string) {\n\t\tif (!isBrowser()) return null;\n\n\t\tconst cookies = parse(document.cookie);\n\t\treturn cookies[name];\n\t}\n\n\tprotected setCookie(name: string, value: string) {\n\t\tif (!isBrowser()) return null;\n\n\t\tdocument.cookie = serialize(name, value, {\n\t\t\t...this.cookieOptions,\n\t\t\thttpOnly: false\n\t\t});\n\t}\n\n\tprotected deleteCookie(name: string) {\n\t\tif (!isBrowser()) return null;\n\n\t\tdocument.cookie = serialize(name, '', {\n\t\t\t...this.cookieOptions,\n\t\t\tmaxAge: 0,\n\t\t\thttpOnly: false\n\t\t});\n\t}\n}\n", "import { Session } from '@supabase/supabase-js';\nimport { parse, serialize } from 'cookie';\nimport { base64url } from 'jose';\n\nexport { parse as parseCookies, serialize as serializeCookie };\n\n/**\n * Based on the environment and the request we know if a secure cookie can be set.\n */\nexport function isSecureEnvironment(headerHost?: string | string[]) {\n\tif (!headerHost) {\n\t\tthrow new Error('The \"host\" request header is not available');\n\t}\n\n\tconst headerHostStr = Array.isArray(headerHost) ? headerHost[0] : headerHost;\n\n\tconst host = (headerHostStr.indexOf(':') > -1 && headerHostStr.split(':')[0]) || headerHostStr;\n\tif (['localhost', '127.0.0.1'].indexOf(host) > -1 || host.endsWith('.local')) {\n\t\treturn false;\n\t}\n\n\treturn true;\n}\n\nexport function parseSupabaseCookie(str: string | null | undefined): Partial<Session> | null {\n\tif (!str) {\n\t\treturn null;\n\t}\n\n\ttry {\n\t\tconst session = JSON.parse(str);\n\t\tif (!session) {\n\t\t\treturn null;\n\t\t}\n\t\t// Support previous cookie which was a stringified session object.\n\t\tif (session.constructor.name === 'Object') {\n\t\t\treturn session;\n\t\t}\n\t\tif (session.constructor.name !== 'Array') {\n\t\t\tthrow new Error(`Unexpected format: ${session.constructor.name}`);\n\t\t}\n\n\t\tconst [_header, payloadStr, _signature] = session[0].split('.');\n\t\tconst payload = base64url.decode(payloadStr);\n\t\tconst decoder = new TextDecoder();\n\n\t\tconst { exp, sub, ...user } = JSON.parse(decoder.decode(payload));\n\n\t\treturn {\n\t\t\texpires_at: exp,\n\t\t\texpires_in: exp - Math.round(Date.now() / 1000),\n\t\t\ttoken_type: 'bearer',\n\t\t\taccess_token: session[0],\n\t\t\trefresh_token: session[1],\n\t\t\tprovider_token: session[2],\n\t\t\tprovider_refresh_token: session[3],\n\t\t\tuser: {\n\t\t\t\tid: sub,\n\t\t\t\tfactors: session[4],\n\t\t\t\t...user\n\t\t\t}\n\t\t};\n\t} catch (err) {\n\t\tconsole.warn('Failed to parse cookie string:', err);\n\t\treturn null;\n\t}\n}\n\nexport function stringifySupabaseSession(session: Session): string {\n\treturn JSON.stringify([\n\t\tsession.access_token,\n\t\tsession.refresh_token,\n\t\tsession.provider_token,\n\t\tsession.provider_refresh_token,\n\t\tsession.user?.factors ?? null\n\t]);\n}\n", "export function isBrowser() {\n\treturn typeof window !== 'undefined' && typeof window.document !== 'undefined';\n}\n", "import { DefaultCookieOptions } from '../types';\n\nexport const DEFAULT_COOKIE_OPTIONS: DefaultCookieOptions = {\n\tpath: '/',\n\tsameSite: 'lax',\n\tmaxAge: 60 * 60 * 24 * 365 * 1000\n};\n", "interface Chunk {\n\tname: string;\n\tvalue: string;\n}\n\nfunction createChunkRegExp(chunkSize: number) {\n\treturn new RegExp('.{1,' + chunkSize + '}', 'g');\n}\n\nconst MAX_CHUNK_SIZE = 3180;\nconst MAX_CHUNK_REGEXP = createChunkRegExp(MAX_CHUNK_SIZE);\n\n/**\n * create chunks from a string and return an array of object\n */\nexport function createChunks(key: string, value: string, chunkSize?: number): Chunk[] {\n\tconst re = chunkSize !== undefined ? createChunkRegExp(chunkSize) : MAX_CHUNK_REGEXP;\n\n\t// check the length of the string to work out if it should be returned or chunked\n\tconst chunkCount = Math.ceil(value.length / (chunkSize ?? MAX_CHUNK_SIZE));\n\n\tif (chunkCount === 1) {\n\t\treturn [{ name: key, value }];\n\t}\n\n\tconst chunks: Chunk[] = [];\n\t// split string into a array based on the regex\n\tconst values = value.match(re);\n\tvalues?.forEach((value, i) => {\n\t\tconst name: string = `${key}.${i}`;\n\t\tchunks.push({ name, value });\n\t});\n\n\treturn chunks;\n}\n\n// Get fully constructed chunks\nexport function combineChunks(\n\tkey: string,\n\tretrieveChunk: (name: string) => string | null | undefined = () => {\n\t\treturn null;\n\t}\n) {\n\tlet values: string[] = [];\n\tfor (let i = 0; ; i++) {\n\t\tconst chunkName = `${key}.${i}`;\n\t\tconst chunk = retrieveChunk(chunkName);\n\n\t\tif (!chunk) {\n\t\t\tbreak;\n\t\t}\n\n\t\tvalues.push(chunk);\n\t}\n\n\treturn values.length ? values.join('') : null;\n}\n", "import { GoTrueClientOptions, Session } from '@supabase/supabase-js';\nimport { DEFAULT_COOKIE_OPTIONS, parseSupabaseCookie, stringifySupabaseSession } from './utils';\nimport { CookieOptions, DefaultCookieOptions } from './types';\nimport { combineChunks, createChunks } from './chunker';\n\nexport interface StorageAdapter extends Exclude<GoTrueClientOptions['storage'], undefined> {}\n\nexport abstract class CookieAuthStorageAdapter implements StorageAdapter {\n\tprotected readonly cookieOptions: DefaultCookieOptions;\n\n\tconstructor(cookieOptions?: CookieOptions) {\n\t\tthis.cookieOptions = {\n\t\t\t...DEFAULT_COOKIE_OPTIONS,\n\t\t\t...cookieOptions,\n\t\t\tmaxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n\t\t};\n\t}\n\n\tprotected abstract getCookie(name: string): string | undefined | null;\n\tprotected abstract setCookie(name: string, value: string): void;\n\tprotected abstract deleteCookie(name: string): void;\n\n\tgetItem(key: string): string | Promise<string | null> | null {\n\t\tconst value = this.getCookie(key);\n\n\t\t// pkce code verifier\n\t\tif (key.endsWith('-code-verifier') && value) {\n\t\t\treturn value;\n\t\t}\n\n\t\tif (value) {\n\t\t\treturn JSON.stringify(parseSupabaseCookie(value));\n\t\t}\n\n\t\tconst chunks = combineChunks(key, (chunkName) => {\n\t\t\treturn this.getCookie(chunkName);\n\t\t});\n\n\t\treturn chunks !== null ? JSON.stringify(parseSupabaseCookie(chunks)) : null;\n\t}\n\n\tsetItem(key: string, value: string): void | Promise<void> {\n\t\t// pkce code verifier\n\t\tif (key.endsWith('-code-verifier')) {\n\t\t\tthis.setCookie(key, value);\n\t\t\treturn;\n\t\t}\n\n\t\tlet session: Session = JSON.parse(value);\n\t\tconst sessionStr = stringifySupabaseSession(session);\n\n\t\t// split session string before setting cookie\n\t\tconst sessionChunks = createChunks(key, sessionStr);\n\n\t\tsessionChunks.forEach((sess) => {\n\t\t\tthis.setCookie(sess.name, sess.value);\n\t\t});\n\t}\n\n\tremoveItem(key: string): void | Promise<void> {\n\t\tthis._deleteSingleCookie(key);\n\t\tthis._deleteChunkedCookies(key);\n\t}\n\n\tprivate _deleteSingleCookie(key: string) {\n\t\tif (this.getCookie(key)) {\n\t\t\tthis.deleteCookie(key);\n\t\t}\n\t}\n\n\tprivate _deleteChunkedCookies(key: string, from = 0) {\n\t\tfor (let i = from; ; i++) {\n\t\t\tconst cookieName = `${key}.${i}`;\n\t\t\tconst value = this.getCookie(cookieName);\n\n\t\t\tif (value === undefined) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tthis.deleteCookie(cookieName);\n\t\t}\n\t}\n}\n", "import { createClient } from '@supabase/supabase-js';\nimport { SupabaseClientOptionsWithoutAuth } from './types';\nimport { isBrowser } from './utils';\nimport { StorageAdapter } from './cookieAuthStorageAdapter';\nimport { GenericSchema } from '@supabase/supabase-js/dist/module/lib/types';\n\nexport function createSupabaseClient<\n\tDatabase = any,\n\tSchemaName extends string & keyof Database = 'public' extends keyof Database\n\t\t? 'public'\n\t\t: string & keyof Database,\n\tSchema extends GenericSchema = Database[SchemaName] extends GenericSchema\n\t\t? Database[SchemaName]\n\t\t: any\n>(\n\tsupabaseUrl: string,\n\tsupabaseKey: string,\n\toptions: SupabaseClientOptionsWithoutAuth<SchemaName> & {\n\t\tauth: {\n\t\t\tstorage: StorageAdapter;\n\t\t\tstorageKey?: string;\n\t\t};\n\t}\n) {\n\tconst browser = isBrowser();\n\n\treturn createClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, {\n\t\t...options,\n\t\tauth: {\n\t\t\tflowType: 'pkce',\n\t\t\tautoRefreshToken: browser,\n\t\t\tdetectSessionInUrl: browser,\n\t\t\tpersistSession: true,\n\t\t\tstorage: options.auth.storage,\n\n\t\t\t// fix this in supabase-js\n\t\t\t...(options.auth?.storageKey\n\t\t\t\t? {\n\t\t\t\t\t\tstorageKey: options.auth.storageKey\n\t\t\t\t  }\n\t\t\t\t: {})\n\t\t}\n\t});\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAcA,YAAQ,QAAQA;AAChB,YAAQ,YAAYC;AAOpB,QAAI,aAAa,OAAO,UAAU;AAUlC,QAAI,qBAAqB;AAczB,aAASD,OAAM,KAAK,SAAS;AAC3B,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACrD;AAEA,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,MAAM,IAAI,UAAU;AAExB,UAAI,QAAQ;AACZ,aAAO,QAAQ,IAAI,QAAQ;AACzB,YAAI,QAAQ,IAAI,QAAQ,KAAK,KAAK;AAGlC,YAAI,UAAU,IAAI;AAChB;AAAA,QACF;AAEA,YAAI,SAAS,IAAI,QAAQ,KAAK,KAAK;AAEnC,YAAI,WAAW,IAAI;AACjB,mBAAS,IAAI;AAAA,QACf,WAAW,SAAS,OAAO;AAEzB,kBAAQ,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI;AAC1C;AAAA,QACF;AAEA,YAAI,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK;AAGvC,YAAI,WAAc,IAAI,GAAG,GAAG;AAC1B,cAAI,MAAM,IAAI,MAAM,QAAQ,GAAG,MAAM,EAAE,KAAK;AAG5C,cAAI,IAAI,WAAW,CAAC,MAAM,IAAM;AAC9B,kBAAM,IAAI,MAAM,GAAG,EAAE;AAAA,UACvB;AAEA,cAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AAAA,QAC/B;AAEA,gBAAQ,SAAS;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AAkBA,aAASC,WAAU,MAAM,KAAK,SAAS;AACrC,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,MAAM,IAAI,UAAU;AAExB,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,UAAI,CAAC,mBAAmB,KAAK,IAAI,GAAG;AAClC,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,UAAI,QAAQ,IAAI,GAAG;AAEnB,UAAI,SAAS,CAAC,mBAAmB,KAAK,KAAK,GAAG;AAC5C,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC/C;AAEA,UAAI,MAAM,OAAO,MAAM;AAEvB,UAAI,QAAQ,IAAI,QAAQ;AACtB,YAAI,SAAS,IAAI,SAAS;AAE1B,YAAI,MAAM,MAAM,KAAK,CAAC,SAAS,MAAM,GAAG;AACtC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AAEA,eAAO,eAAe,KAAK,MAAM,MAAM;AAAA,MACzC;AAEA,UAAI,IAAI,QAAQ;AACd,YAAI,CAAC,mBAAmB,KAAK,IAAI,MAAM,GAAG;AACxC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AAEA,eAAO,cAAc,IAAI;AAAA,MAC3B;AAEA,UAAI,IAAI,MAAM;AACZ,YAAI,CAAC,mBAAmB,KAAK,IAAI,IAAI,GAAG;AACtC,gBAAM,IAAI,UAAU,wBAAwB;AAAA,QAC9C;AAEA,eAAO,YAAY,IAAI;AAAA,MACzB;AAEA,UAAI,IAAI,SAAS;AACf,YAAI,UAAU,IAAI;AAElB,YAAI,CAAC,OAAO,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAChD,gBAAM,IAAI,UAAU,2BAA2B;AAAA,QACjD;AAEA,eAAO,eAAe,QAAQ,YAAY;AAAA,MAC5C;AAEA,UAAI,IAAI,UAAU;AAChB,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,QAAQ;AACd,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WACnC,IAAI,SAAS,YAAY,IACzB,IAAI;AAER,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AAEA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WACnC,IAAI,SAAS,YAAY,IAAI,IAAI;AAErC,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,aAAS,OAAQ,KAAK;AACpB,aAAO,IAAI,QAAQ,GAAG,MAAM,KACxB,mBAAmB,GAAG,IACtB;AAAA,IACN;AASA,aAAS,OAAQ,KAAK;AACpB,aAAO,mBAAmB,GAAG;AAAA,IAC/B;AASA,aAAS,OAAQ,KAAK;AACpB,aAAO,WAAW,KAAK,GAAG,MAAM,mBAC9B,eAAe;AAAA,IACnB;AAUA,aAAS,UAAU,KAAKC,SAAQ;AAC9B,UAAI;AACF,eAAOA,QAAO,GAAG;AAAA,MACnB,SAAS,GAAP;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;AC7QA,IAAAC,iBAAiC;;;ACCjC,oBAAiC;AACjC,SAAS,iBAAiB;AAsBnB,SAAS,oBAAoB,KAAyD;AAC5F,MAAI,CAAC,KAAK;AACT,WAAO;AAAA,EACR;AAEA,MAAI;AACH,UAAM,UAAU,KAAK,MAAM,GAAG;AAC9B,QAAI,CAAC,SAAS;AACb,aAAO;AAAA,IACR;AAEA,QAAI,QAAQ,YAAY,SAAS,UAAU;AAC1C,aAAO;AAAA,IACR;AACA,QAAI,QAAQ,YAAY,SAAS,SAAS;AACzC,YAAM,IAAI,MAAM,sBAAsB,QAAQ,YAAY,MAAM;AAAA,IACjE;AAEA,UAAM,CAAC,SAAS,YAAY,UAAU,IAAI,QAAQ,CAAC,EAAE,MAAM,GAAG;AAC9D,UAAM,UAAU,UAAU,OAAO,UAAU;AAC3C,UAAM,UAAU,IAAI,YAAY;AAEhC,UAAM,EAAE,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,QAAQ,OAAO,OAAO,CAAC;AAEhE,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,YAAY,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAAA,MAC9C,YAAY;AAAA,MACZ,cAAc,QAAQ,CAAC;AAAA,MACvB,eAAe,QAAQ,CAAC;AAAA,MACxB,gBAAgB,QAAQ,CAAC;AAAA,MACzB,wBAAwB,QAAQ,CAAC;AAAA,MACjC,MAAM;AAAA,QACL,IAAI;AAAA,QACJ,SAAS,QAAQ,CAAC;AAAA,QAClB,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,EACD,SAAS,KAAP;AACD,YAAQ,KAAK,kCAAkC,GAAG;AAClD,WAAO;AAAA,EACR;AACD;AAEO,SAAS,yBAAyB,SAA0B;AApEnE;AAqEC,SAAO,KAAK,UAAU;AAAA,IACrB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,MACR,aAAQ,SAAR,mBAAc,YAAW;AAAA,EAC1B,CAAC;AACF;;;AC5EO,SAAS,YAAY;AAC3B,SAAO,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AACpE;;;ACAO,IAAM,yBAA+C;AAAA,EAC3D,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ,KAAK,KAAK,KAAK,MAAM;AAC9B;;;ACDA,SAAS,kBAAkB,WAAmB;AAC7C,SAAO,IAAI,OAAO,SAAS,YAAY,KAAK,GAAG;AAChD;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,kBAAkB,cAAc;AAKlD,SAAS,aAAa,KAAa,OAAe,WAA6B;AACrF,QAAM,KAAK,cAAc,SAAY,kBAAkB,SAAS,IAAI;AAGpE,QAAM,aAAa,KAAK,KAAK,MAAM,UAAU,aAAa,eAAe;AAEzE,MAAI,eAAe,GAAG;AACrB,WAAO,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;AAAA,EAC7B;AAEA,QAAM,SAAkB,CAAC;AAEzB,QAAM,SAAS,MAAM,MAAM,EAAE;AAC7B,mCAAQ,QAAQ,CAACC,QAAO,MAAM;AAC7B,UAAM,OAAe,GAAG,OAAO;AAC/B,WAAO,KAAK,EAAE,MAAM,OAAAA,OAAM,CAAC;AAAA,EAC5B;AAEA,SAAO;AACR;AAGO,SAAS,cACf,KACA,gBAA6D,MAAM;AAClE,SAAO;AACR,GACC;AACD,MAAI,SAAmB,CAAC;AACxB,WAAS,IAAI,KAAK,KAAK;AACtB,UAAM,YAAY,GAAG,OAAO;AAC5B,UAAM,QAAQ,cAAc,SAAS;AAErC,QAAI,CAAC,OAAO;AACX;AAAA,IACD;AAEA,WAAO,KAAK,KAAK;AAAA,EAClB;AAEA,SAAO,OAAO,SAAS,OAAO,KAAK,EAAE,IAAI;AAC1C;;;ACjDO,IAAe,2BAAf,MAAkE;AAAA,EAGxE,YAAY,eAA+B;AAC1C,SAAK,gBAAgB;AAAA,MACpB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,QAAQ,uBAAuB;AAAA,IAChC;AAAA,EACD;AAAA,EAMA,QAAQ,KAAqD;AAC5D,UAAM,QAAQ,KAAK,UAAU,GAAG;AAGhC,QAAI,IAAI,SAAS,gBAAgB,KAAK,OAAO;AAC5C,aAAO;AAAA,IACR;AAEA,QAAI,OAAO;AACV,aAAO,KAAK,UAAU,oBAAoB,KAAK,CAAC;AAAA,IACjD;AAEA,UAAM,SAAS,cAAc,KAAK,CAAC,cAAc;AAChD,aAAO,KAAK,UAAU,SAAS;AAAA,IAChC,CAAC;AAED,WAAO,WAAW,OAAO,KAAK,UAAU,oBAAoB,MAAM,CAAC,IAAI;AAAA,EACxE;AAAA,EAEA,QAAQ,KAAa,OAAqC;AAEzD,QAAI,IAAI,SAAS,gBAAgB,GAAG;AACnC,WAAK,UAAU,KAAK,KAAK;AACzB;AAAA,IACD;AAEA,QAAI,UAAmB,KAAK,MAAM,KAAK;AACvC,UAAM,aAAa,yBAAyB,OAAO;AAGnD,UAAM,gBAAgB,aAAa,KAAK,UAAU;AAElD,kBAAc,QAAQ,CAAC,SAAS;AAC/B,WAAK,UAAU,KAAK,MAAM,KAAK,KAAK;AAAA,IACrC,CAAC;AAAA,EACF;AAAA,EAEA,WAAW,KAAmC;AAC7C,SAAK,oBAAoB,GAAG;AAC5B,SAAK,sBAAsB,GAAG;AAAA,EAC/B;AAAA,EAEQ,oBAAoB,KAAa;AACxC,QAAI,KAAK,UAAU,GAAG,GAAG;AACxB,WAAK,aAAa,GAAG;AAAA,IACtB;AAAA,EACD;AAAA,EAEQ,sBAAsB,KAAa,OAAO,GAAG;AACpD,aAAS,IAAI,QAAQ,KAAK;AACzB,YAAM,aAAa,GAAG,OAAO;AAC7B,YAAM,QAAQ,KAAK,UAAU,UAAU;AAEvC,UAAI,UAAU,QAAW;AACxB;AAAA,MACD;AACA,WAAK,aAAa,UAAU;AAAA,IAC7B;AAAA,EACD;AACD;;;AL5EO,IAAM,kCAAN,cAA8C,yBAAyB;AAAA,EAC7E,YAAY,eAA+B;AAC1C,UAAM,aAAa;AAAA,EACpB;AAAA,EAEU,UAAU,MAAc;AACjC,QAAI,CAAC,UAAU;AAAG,aAAO;AAEzB,UAAM,cAAU,sBAAM,SAAS,MAAM;AACrC,WAAO,QAAQ,IAAI;AAAA,EACpB;AAAA,EAEU,UAAU,MAAc,OAAe;AAChD,QAAI,CAAC,UAAU;AAAG,aAAO;AAEzB,aAAS,aAAS,0BAAU,MAAM,OAAO;AAAA,MACxC,GAAG,KAAK;AAAA,MACR,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA,EAEU,aAAa,MAAc;AACpC,QAAI,CAAC,UAAU;AAAG,aAAO;AAEzB,aAAS,aAAS,0BAAU,MAAM,IAAI;AAAA,MACrC,GAAG,KAAK;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AACD;;;AMnCA,SAAS,oBAAoB;AAMtB,SAAS,qBASf,aACA,aACA,SAMC;AAvBF;AAwBC,QAAM,UAAU,UAAU;AAE1B,SAAO,aAA2C,aAAa,aAAa;AAAA,IAC3E,GAAG;AAAA,IACH,MAAM;AAAA,MACL,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,SAAS,QAAQ,KAAK;AAAA;AAAA,MAGtB,KAAI,aAAQ,SAAR,mBAAc,cACf;AAAA,QACA,YAAY,QAAQ,KAAK;AAAA,MACzB,IACA,CAAC;AAAA,IACL;AAAA,EACD,CAAC;AACF;", "names": ["parse", "serialize", "decode", "import_cookie", "value"]}