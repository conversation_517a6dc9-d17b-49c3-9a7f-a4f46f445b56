import * as Sentry from '@sentry/nextjs'

const SENTRY_DSN = process.env.SENTRY_DSN
const ENVIRONMENT = process.env.NODE_ENV || 'development'

/**
 * 初始化Sentry配置
 */
export function initSentry() {
  if (SENTRY_DSN) {
    Sentry.init({
      dsn: SENTRY_DSN,
      environment: ENVIRONMENT,
      // 性能监控配置
      tracesSampleRate: ENVIRONMENT === 'production' ? 0.1 : 1.0,
      // 会话重放配置
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
      // 忽略特定错误
      ignoreErrors: [
        // 网络错误
        'Network Error',
        'Failed to fetch',
        'NetworkError',
        'AbortError',
        // 第三方脚本错误
        'Script error',
        // 取消操作
        'AbortError',
        'Operation was aborted',
        // 用户权限问题
        'Permission denied',
        'The request is not allowed',
      ],
      // 忽略特定URL
      denyUrls: [
        // 第三方脚本
        /extensions\//i,
        /^chrome:\/\//i,
        /^chrome-extension:\/\//i,
        /^moz-extension:\/\//i,
      ],
      // 自定义上下文
      beforeSend(event) {
        // 在生产环境中移除PII (个人身份信息)
        if (ENVIRONMENT === 'production') {
          if (event.user) {
            // 保留ID但删除其他个人信息
            delete event.user.ip_address
            delete event.user.email
            // 保留用户ID用于跟踪
          }
        }
        return event
      },
    })
  }
}

/**
 * 捕获异常并发送到Sentry
 */
export function captureException(error: unknown, context?: Record<string, any>) {
  if (SENTRY_DSN) {
    Sentry.captureException(error, {
      contexts: {
        custom: context,
      },
    })
  } else {
    // 开发环境下在控制台输出
    console.error('Error captured:', error, context)
  }
}

/**
 * 记录自定义事件
 */
export function captureEvent(name: string, data?: Record<string, any>) {
  if (SENTRY_DSN) {
    Sentry.captureEvent({
      message: name,
      level: 'info',
      extra: data,
    })
  } else {
    console.info('Event captured:', name, data)
  }
}

/**
 * 设置用户上下文
 */
export function setUser(id: string, data?: { username?: string; subscription?: string }) {
  if (SENTRY_DSN) {
    Sentry.setUser({
      id,
      username: data?.username,
      subscription: data?.subscription,
    })
  }
}

/**
 * 清除用户上下文
 */
export function clearUser() {
  if (SENTRY_DSN) {
    Sentry.setUser(null)
  }
}

/**
 * 设置标签
 */
export function setTag(key: string, value: string) {
  if (SENTRY_DSN) {
    Sentry.setTag(key, value)
  }
}

/**
 * 设置额外上下文
 */
export function setContext(name: string, context: Record<string, any>) {
  if (SENTRY_DSN) {
    Sentry.setContext(name, context)
  }
}

/**
 * 开始性能监控
 */
export function startTransaction(name: string, op: string) {
  if (SENTRY_DSN) {
    return Sentry.startTransaction({
      name,
      op,
    })
  }
  return null
}

/**
 * 错误边界组件的HOC
 */
export const withErrorBoundary = Sentry.withErrorBoundary

/**
 * 性能监控组件的HOC
 */
export const withProfiler = Sentry.withProfiler
