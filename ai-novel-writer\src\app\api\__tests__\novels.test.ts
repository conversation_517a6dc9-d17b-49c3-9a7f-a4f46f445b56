import { createMocks } from 'node-mocks-http'
import { GET, POST, PUT, DELETE } from '../novels/route'

// Mock Supabase
jest.mock('@supabase/auth-helpers-nextjs', () => ({
  createRouteHandlerClient: () => ({
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
  }),
}))

describe('/api/novels', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/novels', () => {
    it('should return novels for authenticated user', async () => {
      const { req } = createMocks({
        method: 'GET',
        url: '/api/novels',
      })

      // Mock authenticated user
      const mockUser = { id: 'user-1', email: '<EMAIL>' }
      const mockNovels = [
        {
          id: 'novel-1',
          title: 'Test Novel',
          description: 'A test novel',
          user_id: 'user-1',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ]

      // Mock Supabase responses
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.from().select().eq().order().range().mockResolvedValue({
        data: mockNovels,
        error: null,
      })

      const response = await GET(req)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.novels).toEqual(mockNovels)
    })

    it('should return 401 for unauthenticated user', async () => {
      const { req } = createMocks({
        method: 'GET',
        url: '/api/novels',
      })

      // Mock unauthenticated user
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: new Error('Not authenticated'),
      })

      const response = await GET(req)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('未授权')
    })
  })

  describe('POST /api/novels', () => {
    it('should create a new novel', async () => {
      const { req } = createMocks({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          title: 'New Novel',
          description: 'A new novel description',
          genre: 'fantasy',
        },
      })

      // Mock authenticated user
      const mockUser = { id: 'user-1', email: '<EMAIL>' }
      const mockNovel = {
        id: 'novel-1',
        title: 'New Novel',
        description: 'A new novel description',
        genre: 'fantasy',
        user_id: 'user-1',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      // Mock Supabase responses
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.from().insert().select().single.mockResolvedValue({
        data: mockNovel,
        error: null,
      })

      const response = await POST(req)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.novel).toEqual(mockNovel)
    })

    it('should return 400 for missing title', async () => {
      const { req } = createMocks({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          description: 'A novel without title',
        },
      })

      // Mock authenticated user
      const mockUser = { id: 'user-1', email: '<EMAIL>' }
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      const response = await POST(req)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('标题不能为空')
    })
  })

  describe('PUT /api/novels', () => {
    it('should update an existing novel', async () => {
      const { req } = createMocks({
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          id: 'novel-1',
          title: 'Updated Novel',
          description: 'Updated description',
        },
      })

      // Mock authenticated user
      const mockUser = { id: 'user-1', email: '<EMAIL>' }
      const mockNovel = {
        id: 'novel-1',
        title: 'Updated Novel',
        description: 'Updated description',
        user_id: 'user-1',
        updated_at: '2024-01-01T00:00:00Z',
      }

      // Mock Supabase responses
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { user_id: 'user-1' },
        error: null,
      })
      mockSupabase.from().update().eq().select().single.mockResolvedValue({
        data: mockNovel,
        error: null,
      })

      const response = await PUT(req)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.novel).toEqual(mockNovel)
    })

    it('should return 403 for unauthorized update', async () => {
      const { req } = createMocks({
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          id: 'novel-1',
          title: 'Updated Novel',
        },
      })

      // Mock authenticated user
      const mockUser = { id: 'user-1', email: '<EMAIL>' }
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { user_id: 'different-user' },
        error: null,
      })

      const response = await PUT(req)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('无权限修改此小说')
    })
  })

  describe('DELETE /api/novels', () => {
    it('should delete a novel', async () => {
      const { req } = createMocks({
        method: 'DELETE',
        url: '/api/novels?id=novel-1',
      })

      // Mock authenticated user
      const mockUser = { id: 'user-1', email: '<EMAIL>' }
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { user_id: 'user-1' },
        error: null,
      })
      mockSupabase.from().delete().eq.mockResolvedValue({
        error: null,
      })

      const response = await DELETE(req)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })

    it('should return 400 for missing novel ID', async () => {
      const { req } = createMocks({
        method: 'DELETE',
        url: '/api/novels',
      })

      // Mock authenticated user
      const mockUser = { id: 'user-1', email: '<EMAIL>' }
      const mockSupabase = require('@supabase/auth-helpers-nextjs').createRouteHandlerClient()
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      const response = await DELETE(req)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('小说ID不能为空')
    })
  })
})
