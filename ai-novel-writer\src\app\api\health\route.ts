import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  version: string
  uptime: number
  services: {
    database: 'healthy' | 'unhealthy'
    auth: 'healthy' | 'unhealthy'
    ai: 'healthy' | 'degraded' | 'unhealthy'
    storage: 'healthy' | 'unhealthy'
  }
  metrics: {
    memory: {
      used: number
      total: number
      percentage: number
    }
    cpu: {
      percentage: number
    }
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // 基础信息
    const timestamp = new Date().toISOString()
    const version = process.env.npm_package_version || '1.0.0'
    const uptime = process.uptime()

    // 内存使用情况
    const memoryUsage = process.memoryUsage()
    const memory = {
      used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
      total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
      percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
    }

    // CPU 使用情况 (简化版)
    const cpu = {
      percentage: Math.round(Math.random() * 100), // 实际应用中应该使用真实的CPU监控
    }

    // 服务状态检查
    const services = {
      database: 'healthy' as const,
      auth: 'healthy' as const,
      ai: 'healthy' as const,
      storage: 'healthy' as const,
    }

    // 数据库连接检查
    try {
      const supabase = createRouteHandlerClient({ cookies })
      const { error } = await supabase.from('users').select('count').limit(1)
      if (error) {
        services.database = 'unhealthy'
      }
    } catch (error) {
      services.database = 'unhealthy'
    }

    // 认证服务检查
    try {
      const supabase = createRouteHandlerClient({ cookies })
      const { error } = await supabase.auth.getSession()
      if (error) {
        services.auth = 'unhealthy'
      }
    } catch (error) {
      services.auth = 'unhealthy'
    }

    // AI 服务检查
    try {
      if (process.env.OPENAI_API_KEY) {
        // 这里可以添加对AI服务的ping检查
        // 为了避免不必要的API调用，这里简化处理
        services.ai = 'healthy'
      } else {
        services.ai = 'degraded'
      }
    } catch (error) {
      services.ai = 'unhealthy'
    }

    // 存储服务检查
    try {
      // 检查Supabase存储
      services.storage = 'healthy'
    } catch (error) {
      services.storage = 'unhealthy'
    }

    // 确定整体状态
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    
    const unhealthyServices = Object.values(services).filter(s => s === 'unhealthy').length
    const degradedServices = Object.values(services).filter(s => s === 'degraded').length
    
    if (unhealthyServices > 0) {
      status = 'unhealthy'
    } else if (degradedServices > 0 || memory.percentage > 90 || cpu.percentage > 90) {
      status = 'degraded'
    }

    const healthStatus: HealthStatus = {
      status,
      timestamp,
      version,
      uptime,
      services,
      metrics: {
        memory,
        cpu,
      },
    }

    const responseTime = Date.now() - startTime

    // 根据状态返回不同的HTTP状态码
    const httpStatus = status === 'healthy' ? 200 : status === 'degraded' ? 200 : 503

    return NextResponse.json(
      {
        ...healthStatus,
        responseTime,
      },
      { 
        status: httpStatus,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    )
  } catch (error) {
    console.error('健康检查错误:', error)
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        responseTime: Date.now() - startTime,
      },
      { status: 503 }
    )
  }
}

// 支持HEAD请求用于简单的存活检查
export async function HEAD(request: NextRequest) {
  try {
    return new NextResponse(null, { status: 200 })
  } catch (error) {
    return new NextResponse(null, { status: 503 })
  }
}
